## HIPAA Compliance with RLS Policies

The RLS policies already implemented are providing comprehensive access control at the database level.

To prevent accidental changes that could break the database, we must always edit Row Level Security (RLS) policies through code migrations—never through the Supabase GUI. This is a limitation of Supabase: there's no way to disable GUI editing of these security rules.

### Current Security Status

✅ **Already Implemented:**

- Row Level Security (RLS) policies for all tables
- Role-based access control (CLINICIAN, GROUP_ADMIN)
- Team-based data isolation
- Patient self-access controls
- Authentication via Supabase Auth
- Basic input validation with Yup schemas
- JSON schema validation for exercise parameters

### Required HIPAA Additions

The only additional components needed for full HIPAA compliance are:

1. **Audit Logging** (database triggers)
2. **Data Encryption** (field-level encryption)
3. **Testing & Monitoring** (security validation)
4. **Session Management** (automatic termination)
5. **Enhanced Input Validation** (comprehensive sanitization)

### 1. HIPAA Compliance Implementation

#### 1.1 Database Schema Additions

Create new migration `supabase/migrations/20250819000000_hipaa_compliance.sql`:

````sql
-- Audit logging table for HIPAA compliance
CREATE TABLE audit_logs (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id),
  action text NOT NULL,
  table_name text NOT NULL,
  record_id uuid,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  timestamp timestamptz DEFAULT now()
);

-- Enable RLS on audit_logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "admins_can_view_audit_logs" ON audit_logs
FOR SELECT TO authenticated
USING ((auth.jwt() ->> 'user_role')::text = 'GROUP_ADMIN');

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values)
  VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END
  );
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Add audit triggers to all PHI tables
CREATE TRIGGER audit_patients_trigger
  AFTER INSERT OR UPDATE OR DELETE ON patients
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_exercise_params_trigger
  AFTER INSERT OR UPDATE OR DELETE ON exercise_params
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_reps_completed_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reps_completed
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_sets_completed_trigger
  AFTER INSERT OR UPDATE OR DELETE ON sets_completed
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_workout_activity_trigger
  AFTER INSERT OR UPDATE OR DELETE ON workout_activity
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Add encryption for sensitive fields (example for patient name)
-- Note: This requires pgcrypto extension
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Add encrypted columns to patients table
ALTER TABLE patients ADD COLUMN name_encrypted bytea;
ALTER TABLE patients ADD COLUMN name_iv bytea;

-- Function to encrypt patient name
CREATE OR REPLACE FUNCTION encrypt_patient_name()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.name IS NOT NULL THEN
    NEW.name_iv := gen_random_bytes(16);
    NEW.name_encrypted := pgp_sym_encrypt(NEW.name, current_setting('app.encryption_key'), 'cipher-algo=aes256');
    NEW.name := NULL; -- Clear plaintext
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to decrypt patient name
CREATE OR REPLACE FUNCTION decrypt_patient_name(encrypted_data bytea)
RETURNS text AS $$
BEGIN
  IF encrypted_data IS NULL THEN
    RETURN NULL;
  END IF;
  RETURN pgp_sym_decrypt(encrypted_data, current_setting('app.encryption_key'), 'cipher-algo=aes256');
END;
$$ LANGUAGE plpgsql;

-- Create a view that automatically decrypts patient names
CREATE OR REPLACE VIEW patients_decrypted AS
SELECT
  id,
  decrypt_patient_name(name_encrypted) as name,
  email,
  date_of_birth,
  created_at,
  updated_at,
  team_id,
  group_id,
  affected_side,
  rep_goal,
  days_per_week_goal
FROM patients;

-- Trigger to encrypt patient names
CREATE TRIGGER encrypt_patient_name_trigger
  BEFORE INSERT OR UPDATE ON patients
  FOR EACH ROW EXECUTE FUNCTION encrypt_patient_name();
````

#### 1.2 Application Usage

**For Reading Data (Use the decrypted view - SINGLE FETCH):**
```sql
-- Instead of: SELECT * FROM patients
-- Use: SELECT * FROM patients_decrypted
-- This automatically decrypts names and gives you readable data
```

**For Writing Data (Use the original table):**
```sql
-- Insert/Update operations work normally
INSERT INTO patients (name, email, date_of_birth, team_id)
VALUES ('John Doe', '<EMAIL>', '1990-01-01', team_uuid);

-- The trigger automatically encrypts the name
```

**In the application:**
```typescript
// Just change the table name in the existing queries
export const fetchPatientWorkouts = async (
  patientId: string,
  supabaseClient: SupabaseClient<Database>,
): Promise<Patient> => {
  // Single fetch with decrypted names
  const { data: patient, error } = await supabaseClient
    .from('patients_decrypted')  // ← Only change needed!
    .select(
      `id,
      name,  // ← This is now automatically decrypted
      team_id,
      affected_side,
      rep_goal,
      days_per_week_goal,
      workout(
        id,
        category,
        name,
        last_completed,
        workout_activity(id, current_exercise, activity_timestamp, sets_completed(created_at, exercise_params_id)),
        exercise_params(
          *,
          exercise(
            *,
            audio(*),
            video(*),
            exercise_category(*)
          )
        )
      )`,
    )
    .eq('id', patientId)
    .single();

  // Rest of your existing code works exactly the same!
  // ...
};
```

#### 1.3 Archive logs database table automated cleanup 
We should archive audit logs to **keep the database lightweight and performant** while ensuring long-term retention of security events as required by HIPAA.
- **Performance**: Prevents the `audit_logs` table from growing indefinitely, which can slow down queries and backups.  
- **Compliance**: HIPAA requires audit records to be retained for multiple years.  
- **Cost Optimization**: Storing older logs in encrypted cloud storage (e.g., Amazon S3) is significantly cheaper than keeping them in Postgres.  

**How to implement this backup**:
1. Deploy an [Edge Function](https://supabase.com/docs/guides/functions) (`archive-logs`) to Supabase.  
2. Create a cron job that runs the function every week  
3. The function:
   - Selects all logs older than 7 days `0 0 * * 0`  (Run every Sunday at midnight (UTC))
   - Exports them as a JSON file.
   - Uploads the file to an encrypted S3 bucket.
   - Deletes the archived logs from the database.

- **Database**: Keep only the last 7 days of logs.
- **S3 Bucket**: Keep JSON archives for 6 years (HIPAA minimum requirement).

***Database cleanup function example***:
```ts
import { createClient } from '@supabase/supabase-js';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
const s3 = new S3Client({
  region: 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function archiveAuditLogs() {
  const now = Date.now()
  const 7_days_ago = Date(now - 7 * 24 * 60 * 60 * 1000)

  // Get all the data that is older than 7 days ago
  const { data: logs, error } = await supabase
    .from('audit_logs')
    .select('*')
    .lt('timestamp', 7_days_ago.toISOString());

  if (error || !logs?.length) return;

  await s3.send(new PutObjectCommand({
    Bucket: process.env.AUDIT_LOGS_BUCKET!,
    Key: `audit_logs_${7_days_ago}.json`,
    Body: JSON.stringify(logs),
    ContentType: 'application/json',
    ServerSideEncryption: 'AES256',
  }));

  const ids = logs.map(l => l.id);
  await supabase.from('audit_logs').delete().in('id', ids);
}
```

How to set the env variables:
```
supabase secrets set \
  AWS_ACCESS_KEY_ID=xxxx \
  AWS_SECRET_ACCESS_KEY=yyyy \
  AUDIT_LOGS_BUCKET=my-audit-logs
```


### 2. Session Management Implementation

#### 2.1 Automatic Session Termination

**Current Issue:** JWT tokens expire after 1 hour but no automatic session termination on inactivity.

**Required Implementation:**

```typescript
// src/hooks/useSessionTimeout.ts
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useRouter } from 'next/router';
import { useIdleTimer } from 'react-idle-timer';
import { useRef, useEffect } from 'react';
import { useSessionPauseStore } from '@/stores/sessionPauseStore'; // Assume you create this zustand store

const INACTIVITY_TIMEOUT = 15 * 60 * 1000; // 15 minutes
const WARNING_TIMEOUT = 5 * 60 * 1000; // 5 minutes before timeout

export const useSessionTimeout = () => {
  const supabase = useSupabaseClient();
  const router = useRouter();

  const idleTimerRef = useRef<any>(null);
  const isPaused = useSessionPauseStore((s) => s.isPaused);

  // Called when user is idle for (INACTIVITY_TIMEOUT - WARNING_TIMEOUT)
  const onPrompt = () => {
    // Show warning modal/toast
  };

  // Called when user is idle for INACTIVITY_TIMEOUT
  const onIdle = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  const { start, pause } = useIdleTimer({
    timeout: INACTIVITY_TIMEOUT,
    onIdle,
    onPrompt,
    promptBeforeIdle: WARNING_TIMEOUT,
    crossTab: true,
    events: [
      'mousemove',
      'mousedown',
      'keydown',
      'touchstart',
      'scroll',
    ],
  });

  useEffect(() => {
    if (idleTimerRef.current) {
      if (isPaused) {
        pause();
      } else {
        start();
      }
    }
  }, [isPaused]);
};
```

**Update Supabase Config:**
```toml
[auth]
jwt_expiry = 900  # 15 minutes instead of 1 hour
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 5
```

### 3. Enhanced Input Validation

#### 3.1 Input Sanitization

**Current Issue:** Basic Yup validation exists but lacks comprehensive sanitization.

**Required Implementation:**

```typescript
// src/utils/inputSanitization.ts
import DOMPurify from 'dompurify';

export const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

export const validatePatientName = (name: string): boolean => {
  const sanitized = sanitizeInput(name);
  return sanitized.length >= 1 && sanitized.length <= 100;
};

export const validateNumericInput = (value: number, min: number, max: number): boolean => {
  return Number.isInteger(value) && value >= min && value <= max;
};
```
Also, input sanitization should be added in the current signup Deno function (`supabase/functions/signup-patient/index.ts`).

**Update Form Validation:**
```typescript
// Enhanced validation schema
const validationSchema = Yup.object({
  name: Yup.string()
    .required(t('name-required'))
    .test('sanitized', 'Invalid characters', (value) => 
      value ? validatePatientName(value) : false
    ),
  email: Yup.string()
    .email(t('email-invalid'))
    .required(t('email-required'))
    .test('valid-email', 'Invalid email format', (value) =>
      value ? validateEmail(value) : false
    ),
  repGoal: Yup.number()
    .test('valid-range', t('rep-goal-invalid'), (value) =>
      value ? validateNumericInput(value, 10, 2000) : false
    )
    .required(t('rep-goal-required')),
});
```

### 4. Testing & Security Implementation

#### 4.1 Security Testing Infrastructure

**Current Issue:** No testing infrastructure exists for security validation.

**Required Implementation:**

In a setup file, setup a temporary local supabase db with static data

```typescript
// src/tests/security/RLSPolicyTests.test.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/supabase';

describe('RLS Policy Tests', () => {
  let supabase1: any;
  let supabase2: any;

  beforeEach(async () => {
    // Create two different authenticated sessions
    supabase1 = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    supabase2 = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Sign in with different users
    await supabase1.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });
    await supabase2.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });
  });

  test('Users cannot access other users patient data', async () => {
    // User 1 creates a patient
    const { data: patient1 } = await supabase1
      .from('patients')
      .insert({
        name: 'Test Patient 1',
        email: '<EMAIL>',
        team_id: 'team-uuid-1'
      })
      .select()
      .single();

    // User 2 tries to access patient 1's data
    const { data: patient2, error } = await supabase2
      .from('patients')
      .select('*')
      .eq('id', patient1.id)
      .single();

    // Should be denied access
    expect(error).toBeTruthy();
    expect(patient2).toBeNull();
  });

  test('Audit logs are created for all PHI operations', async () => {
    const { data: patient } = await supabase1
      .from('patients')
      .insert({
        name: 'Audit Test Patient',
        email: '<EMAIL>',
        team_id: 'team-uuid-1'
      })
      .select()
      .single();

    // Check audit log was created
    const { data: auditLog } = await supabase1
      .from('audit_logs')
      .select('*')
      .eq('table_name', 'patients')
      .eq('record_id', patient.id)
      .single();

    expect(auditLog).toBeTruthy();
    expect(auditLog.action).toBe('INSERT');
  });
});
```

#### 4.2 GitHub Actions Integration

**Add to .github/workflows/test.yml:**
```yaml
name: Database Security Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:security
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.TEST_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.TEST_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.TEST_SUPABASE_SERVICE_ROLE_KEY }}
```

#### 4.3 Security breach logging

These examples are minimal actions that can be done to monitor security breaches on the app.
Use a tool like [Supabase Edge Functions](https://supabase.com/docs/guides/functions)

- Implement monitoring for multiple failed login attempts: if a user enters incorrect authentication information more than X times, temporarily block further login attempts for that user (account lockout or rate limiting).
  **Example Implementation:**
  - Store failed attempt counts and timestamps in a database table (e.g., `failed_logins`).
  - On each login attempt, check the count for the user:
    - If the threshold (e.g., 5 failed attempts within 15 minutes) is exceeded, block login and return an error message.
- Log unauthorized access attempts: When users try to access patient data they shouldn't
  **Example Implementation**
  Record the event with user ID, attempted resource, timestamp, and IP address in a table such as `unauthorized_access` (columns: id, user_id, patient_id, attempted_action, timestamp, ip_address, user_agent).
- After-hours access monitoring
  **Exmaple Implementation**
  Track and flag access to sensitive data outside of normal business hours. Store these events in a table such as `after_hours_access` (columns: id, user_id, accessed_resource, timestamp, action, ip_address).
- Rate limiting
  **Example Implementation**
  Prevent automated attacks by tracking request frequency per user/IP in a table like `rate_limit_events` (columns: id, user_id, ip_address, endpoint, request_count, window_start, window_end, blocked_until). 
  Block or throttle users who exceed thresholds.

### 5. Implementation Steps

#### Phase 1: HIPAA Compliance (Before Production)

- Deploy audit logging database migration
- Add encryption for sensitive fields
- Implement session timeout management
- Add comprehensive input sanitization
- Set up testing infrastructure
- Implement security monitoring

#### Phase 2: Security Testing

- Create a procedure for code reviews to make sure HIPAA is not broken in the app.
- Run RLS policy tests
- Validate audit logging
- Test authentication flows
- Performance testing

#### Phase 3: Production Deployment

- Deploy to dev with full testing
- Security audit review
- Production deployment
- Ongoing monitoring
- (Optional) Create a monitoring dashboard for admins to see who did what and monitor the failed login attempts

### 6. HIPAA Compliance Checklist

#### Technical Safeguards

- ✅ Row Level Security (RLS) - **Implemented**
- ✅ Authentication & Authorization - **Implemented**
- ✅ Basic Input Validation - **Implemented**
- ❌ Audit logging system
- ❌ Data encryption at rest
- ❌ Automatic session termination (15 min timeout)
- ❌ Enhanced input validation & sanitization
- ❌ Security testing infrastructure

#### Administrative Safeguards

- ❌ Create procedures for every new deployment
- ❌ Access management procedures
- ❌ Security incident procedures
- ❌ Contingency planning
- ❌ Workforce training requirements
