# Hosting solutions

Vercel's HIPAA-compliant hosting is only available on its Enterprise plan, which is significantly more expensive than typical developer-focused solutions:

### Vercel Enterprise (Required for HIPAA):

Recent benchmarks and user reports indicate pricing starts at ~$22,000–$45,000USD per year (about $2,000–$3,750/month), depending on negotiation, number of seats, and included features like Secure Compute, SSO, dedicated cloud, custom SLAs, and more.

Smaller startups typically pay around $2,000–$3,000/month for HIPAA enterprise features; larger businesses may pay substantially more.

You must sign a Business Associate Agreement (BAA) and utilize "Secure Compute" for the isolation and compliance required under HIPAA.

### AWS Amplify (HIPAA eligible by default):

AWS Amplify pricing is strictly pay-as-you-go, with no minimum monthly fee and with generous free tier limits (up to 500,000 SSR requests and 15GB of data transfer per month free, then $0.30/million SSR requests and $0.15/GB outbound transfer).

Example: A startup with 300 daily users on Amplify could pay under $10/month, while even much heavier usage (10,000 daily users) might total only $65–$80/month.

No enterprise-level minimum contract is required to access HIPAA eligibility: all Amplify customers on eligible services can be covered under an AWS BAA.

## Risks of NextJS SSR "Magic" on Vercel

### Server-Side Rendering Risks:

NextJS automatically handles server-side rendering (SSR) and static site generation (SSG), which can inadvertently expose patient data to Vercel's servers:

- **Automatic SSR**: NextJS may automatically render pages on the server, potentially including patient data in server logs or temporary storage
- **getServerSideProps**: Any data fetched in `getServerSideProps` is processed on Vercel's servers, creating HIPAA compliance risks
- **API Routes**: Serverless functions run on Vercel's infrastructure, potentially exposing patient data
- **Caching**: Vercel's edge caching may store sensitive data in their global network

### Developer Error Risks:

A simple developer mistake can instantly make the app non-HIPAA compliant:

- **Accidental SSR**: Using `getServerSideProps` instead of `getStaticProps` or client-side fetching
- **Debug Logging**: Adding console.log statements that include patient data in server logs
- **Error Boundaries**: Server-side error handling that might log patient information
- **Third-party Integrations**: Adding analytics or monitoring tools that process data on Vercel's servers
- **Environment Variables**: Misconfiguring environment variables that expose sensitive data

### Compliance Fragility:

The current setup is fragile because:
- Any future feature requiring server-side data processing breaks HIPAA compliance
- Code reviews must catch every potential SSR exposure
- Third-party dependencies might introduce server-side processing
- Vercel's platform updates could change how data is handled

## Summary & Recommendation

### Cost Comparison:
- **Vercel Enterprise**: $22,000–$45,000/year ($2,000–$3,750/month) - Required for HIPAA compliance
- **AWS Amplify**: $10–$100+/month pay-as-you-go - HIPAA eligible by default

### Risk Assessment:

**Vercel (Non-Enterprise)**: 
- **High Risk**: NextJS SSR "magic" can inadvertently expose patient data through automatic server-side rendering, API routes, and caching
- **Fragile Compliance**: A single developer error (accidental SSR, debug logging, third-party integrations) instantly breaks HIPAA compliance
- **Future Limitations**: Any server-side data processing requires expensive Enterprise upgrade

**AWS Amplify**:
- **Low Risk**: HIPAA eligible by default with proper BAA coverage
- **Stable Compliance**: No risk of accidental data exposure through platform features
- **Future-Proof**: Can handle server-side processing without compliance concerns
- **Performance Trade-off**: Generally slower than Vercel's edge network and optimized infrastructure - Can be improved with a larger EC2 server on AWS but could be moved in the future if the app scales to a larger audience and if fast performance is required.

### Current State Analysis:
While the app currently avoids server-side patient data processing (only authentication JWT info in getServerSideProps), this setup is inherently fragile. The combination of NextJS automatic SSR, developer error risks, and platform dependencies creates ongoing compliance vulnerability.

### Final Recommendation:
**Move to AWS Amplify** for HIPAA compliance. The cost savings ($2,000+/month vs $10-100/month) combined with eliminated compliance risks make this the clear choice for healthcare applications. Vercel Enterprise is only viable for large enterprises with dedicated compliance budgets.